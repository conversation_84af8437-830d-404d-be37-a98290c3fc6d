/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M15 4 5 9", key: "14bkc9" }],
  ["path", { d: "m15 8.5-10 5", key: "1grtsx" }],
  ["path", { d: "M18 12a9 9 0 0 1-9 9V3", key: "1sst7f" }]
];
const TurkishLira = createLucideIcon("turkish-lira", __iconNode);

export { __iconNode, TurkishLira as default };
//# sourceMappingURL=turkish-lira.js.map
