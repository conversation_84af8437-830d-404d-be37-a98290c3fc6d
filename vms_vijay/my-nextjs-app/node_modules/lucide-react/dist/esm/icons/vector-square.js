/**
 * @license lucide-react v0.540.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */

import createLucideIcon from '../createLucideIcon.js';

const __iconNode = [
  ["path", { d: "M19.5 7a24 24 0 0 1 0 10", key: "8n60xe" }],
  ["path", { d: "M4.5 7a24 24 0 0 0 0 10", key: "2lmadr" }],
  ["path", { d: "M7 19.5a24 24 0 0 0 10 0", key: "1q94o2" }],
  ["path", { d: "M7 4.5a24 24 0 0 1 10 0", key: "2z8ypa" }],
  ["rect", { x: "17", y: "17", width: "5", height: "5", rx: "1", key: "1ac74s" }],
  ["rect", { x: "17", y: "2", width: "5", height: "5", rx: "1", key: "1e7h5j" }],
  ["rect", { x: "2", y: "17", width: "5", height: "5", rx: "1", key: "1t4eah" }],
  ["rect", { x: "2", y: "2", width: "5", height: "5", rx: "1", key: "940dhs" }]
];
const VectorSquare = createLucideIcon("vector-square", __iconNode);

export { __iconNode, VectorSquare as default };
//# sourceMappingURL=vector-square.js.map
